const Razorpay = require("razorpay");

// Initialize Razorpay instance with error handling
let razorpay = null;

const initializeRazorpay = () => {
  if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
    console.warn(
      "Razorpay credentials not found. Using test mode or check your .env file."
    );
    // Return a mock instance for development
    return {
      orders: {
        create: () => Promise.reject(new Error("Razorpay not configured")),
      },
      payments: {
        fetch: () => Promise.reject(new Error("Razorpay not configured")),
      },
    };
  }

  return new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID,
    key_secret: process.env.RAZORPAY_KEY_SECRET,
  });
};

// Lazy initialization
const getRazorpayInstance = () => {
  if (!razorpay) {
    razorpay = initializeRazorpay();
  }
  return razorpay;
};

// Razorpay configuration options
const razorpayConfig = {
  currency: "INR",
  receipt_prefix: "order_rcptid_",
  payment_capture: 1, // Auto capture payments
  theme: {
    color: "#3399cc",
  },
  prefill: {
    method: "card",
  },
};

module.exports = {
  get razorpay() {
    return getRazorpayInstance();
  },
  razorpayConfig,
};
