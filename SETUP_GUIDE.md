# Quick Setup Guide

## 🚀 Getting Started

### 1. Configure Razorpay Credentials

To fix the Razorpay error, you need to set up your API credentials:

#### Option A: Get Test Credentials (Recommended for Development)
1. Go to [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Sign up for a free account if you don't have one
3. Navigate to **Settings** → **API Keys**
4. Copy your **Test Key ID** and **Test Key Secret**

#### Option B: Use Demo Credentials (For Testing Only)
```env
RAZORPAY_KEY_ID=rzp_test_1234567890
RAZORPAY_KEY_SECRET=demo_secret_key_1234567890
```

### 2. Update Your .env File

Open the `.env` file and replace the placeholder values:

```env
# Replace these with your actual Razorpay credentials
RAZORPAY_KEY_ID=rzp_test_your_actual_key_id
RAZORPAY_KEY_SECRET=your_actual_secret_key

# Also update this with a secure random string
JWT_SECRET=your_super_secret_jwt_key_make_it_long_and_random_123456789
```

### 3. Start MongoDB

Make sure MongoDB is running on your system:

#### Windows:
```bash
# If MongoDB is installed as a service
net start MongoDB

# Or start manually
mongod
```

#### macOS/Linux:
```bash
# Using Homebrew (macOS)
brew services start mongodb-community

# Or start manually
mongod
```

### 4. Install Dependencies and Start

```bash
# Install all dependencies
npm install

# Start the development server
npm run dev
```

### 5. Test the Application

1. Open your browser and go to `http://localhost:3000`
2. Register a new account
3. Try making a test payment

## 🔧 Troubleshooting

### Error: `key_id` or `oauthToken` is mandatory
- **Solution**: Make sure your `.env` file has valid Razorpay credentials
- **Check**: Ensure there are no spaces around the `=` sign in your `.env` file

### MongoDB Connection Error
- **Solution**: Make sure MongoDB is running
- **Alternative**: Use MongoDB Atlas (cloud) by updating `MONGODB_URI` in `.env`

### Port Already in Use
- **Solution**: Change the `PORT` in your `.env` file to a different number (e.g., 3001)

### Payment Not Working
- **Check**: Make sure you're using test credentials for development
- **Note**: Test payments won't actually charge money

## 📝 Sample .env Configuration

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/razorpay_db

# Razorpay Configuration (REPLACE WITH YOUR ACTUAL KEYS)
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_key_secret_here

# JWT Configuration (REPLACE WITH A SECURE RANDOM STRING)
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Other configurations
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 🎯 Next Steps

1. **Get Real Razorpay Account**: For production, you'll need to complete KYC verification
2. **Set Up Webhooks**: Configure webhooks in Razorpay dashboard for real-time updates
3. **Add More Payment Methods**: Enable UPI, wallets, net banking, etc.
4. **Customize UI**: Modify the frontend to match your brand
5. **Add Tests**: Write more comprehensive tests for your application

## 📞 Need Help?

- Check the main [README.md](./README.md) for detailed documentation
- Visit [Razorpay Documentation](https://razorpay.com/docs/)
- Create an issue if you encounter problems

## 🔒 Security Notes

- Never commit your `.env` file to version control
- Use different credentials for development and production
- Regularly rotate your API keys
- Enable webhook signature verification for production
