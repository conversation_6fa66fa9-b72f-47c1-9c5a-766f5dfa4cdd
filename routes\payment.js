const express = require("express");
const { body } = require("express-validator");
const {
  createOrder,
  verifyPayment,
  handlePaymentFailure,
  getPaymentDetails,
} = require("../controllers/paymentController");
const { protect } = require("../middleware/auth");

const router = express.Router();

// Validation rules
const createOrderValidation = [
  body("amount")
    .isNumeric()
    .isFloat({ min: 1 })
    .withMessage("Amount must be a number greater than 0"),
  body("currency")
    .optional()
    .isIn(["INR", "USD", "EUR"])
    .withMessage("Currency must be INR, USD, or EUR"),
];

const verifyPaymentValidation = [
  body("razorpay_order_id")
    .notEmpty()
    .withMessage("Razorpay order ID is required"),
  body("razorpay_payment_id")
    .notEmpty()
    .withMessage("Razorpay payment ID is required"),
  body("razorpay_signature")
    .notEmpty()
    .withMessage("Razorpay signature is required"),
];

// Get Razorpay key for frontend
router.get("/config", (req, res) => {
  res.json({
    success: true,
    data: {
      key: process.env.RAZORPAY_KEY_ID || "rzp_test_demo_key",
    },
  });
});

// Routes
router.post("/create-order", protect, createOrderValidation, createOrder);
router.post("/verify", protect, verifyPaymentValidation, verifyPayment);
router.post("/failure", protect, handlePaymentFailure);
router.get("/:paymentId", protect, getPaymentDetails);

module.exports = router;
