// Global variables
let currentUser = null;
let authToken = localStorage.getItem("authToken");

// API Base URL
const API_BASE = "/api";

// DOM Elements
const authModal = document.getElementById("authModal");
const authForm = document.getElementById("authForm");
const loginBtn = document.getElementById("loginBtn");
const registerBtn = document.getElementById("registerBtn");
const logoutBtn = document.getElementById("logoutBtn");
const dashboardLink = document.getElementById("dashboardLink");
const payBtn = document.getElementById("payBtn");
const getStartedBtn = document.getElementById("getStartedBtn");
const loadingSpinner = document.getElementById("loadingSpinner");
const toastContainer = document.getElementById("toastContainer");

// Initialize app
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
  setupEventListeners();
});

// Initialize application
async function initializeApp() {
  if (authToken) {
    try {
      await getCurrentUser();
      updateUIForLoggedInUser();
    } catch (error) {
      console.error("Failed to get current user:", error);
      logout();
    }
  }
}

// Setup event listeners
function setupEventListeners() {
  // Auth buttons
  loginBtn.addEventListener("click", () => showAuthForm("login"));
  registerBtn.addEventListener("click", () => showAuthForm("register"));
  logoutBtn.addEventListener("click", logout);

  // Payment button
  payBtn.addEventListener("click", handlePayment);

  // Get started button
  getStartedBtn.addEventListener("click", () => {
    if (currentUser) {
      document.getElementById("payment").scrollIntoView({ behavior: "smooth" });
    } else {
      showAuthForm("register");
    }
  });

  // Modal close
  document.querySelector(".close").addEventListener("click", closeModal);
  window.addEventListener("click", (e) => {
    if (e.target === authModal) {
      closeModal();
    }
  });

  // Navigation
  document.querySelectorAll(".nav-link").forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();
      const target = e.target.getAttribute("href");
      if (target.startsWith("#")) {
        document.querySelector(target).scrollIntoView({ behavior: "smooth" });
      }
    });
  });
}

// API Helper functions
async function apiCall(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const config = {
    headers: {
      "Content-Type": "application/json",
      ...(authToken && { Authorization: `Bearer ${authToken}` }),
    },
    ...options,
  };

  if (config.body && typeof config.body === "object") {
    config.body = JSON.stringify(config.body);
  }

  const response = await fetch(url, config);
  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || "API request failed");
  }

  return data;
}

// Authentication functions
async function getCurrentUser() {
  const response = await apiCall("/auth/me");
  currentUser = response.data.user;
  return currentUser;
}

function showAuthForm(type) {
  const isLogin = type === "login";
  authForm.innerHTML = `
        <h2>${isLogin ? "Login" : "Register"}</h2>
        <form id="authFormElement">
            ${!isLogin ? '<input type="text" id="name" placeholder="Full Name" required>' : ""}
            <input type="email" id="email" placeholder="Email" required>
            <input type="password" id="password" placeholder="Password" required>
            ${!isLogin ? '<input type="tel" id="phone" placeholder="Phone Number" required>' : ""}
            <button type="submit" class="btn btn-primary btn-large">${isLogin ? "Login" : "Register"}</button>
        </form>
        <p style="text-align: center; margin-top: 20px;">
            ${isLogin ? "Don't have an account?" : "Already have an account?"}
            <a href="#" id="switchAuth">${isLogin ? "Register" : "Login"}</a>
        </p>
    `;

  // Add form styles
  const formInputs = authForm.querySelectorAll("input");
  formInputs.forEach((input) => {
    input.style.cssText = `
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        `;
  });

  // Setup form submission
  document.getElementById("authFormElement").addEventListener("submit", (e) => {
    e.preventDefault();
    if (isLogin) {
      handleLogin();
    } else {
      handleRegister();
    }
  });

  // Setup auth switch
  document.getElementById("switchAuth").addEventListener("click", (e) => {
    e.preventDefault();
    showAuthForm(isLogin ? "register" : "login");
  });

  authModal.style.display = "block";
}

async function handleLogin() {
  try {
    showLoading(true);
    const email = document.getElementById("email").value;
    const password = document.getElementById("password").value;

    const response = await apiCall("/auth/login", {
      method: "POST",
      body: { email, password },
    });

    authToken = response.data.token;
    localStorage.setItem("authToken", authToken);
    currentUser = response.data.user;

    closeModal();
    updateUIForLoggedInUser();
    showToast("Login successful!", "success");
  } catch (error) {
    showToast(error.message, "error");
  } finally {
    showLoading(false);
  }
}

async function handleRegister() {
  try {
    showLoading(true);
    const name = document.getElementById("name").value;
    const email = document.getElementById("email").value;
    const password = document.getElementById("password").value;
    const phone = document.getElementById("phone").value;

    const response = await apiCall("/auth/register", {
      method: "POST",
      body: { name, email, password, phone },
    });

    authToken = response.data.token;
    localStorage.setItem("authToken", authToken);
    currentUser = response.data.user;

    closeModal();
    updateUIForLoggedInUser();
    showToast("Registration successful!", "success");
  } catch (error) {
    showToast(error.message, "error");
  } finally {
    showLoading(false);
  }
}

function logout() {
  authToken = null;
  currentUser = null;
  localStorage.removeItem("authToken");
  updateUIForLoggedOutUser();
  showToast("Logged out successfully!", "success");
}

function updateUIForLoggedInUser() {
  loginBtn.style.display = "none";
  registerBtn.style.display = "none";
  logoutBtn.style.display = "inline-block";
  dashboardLink.style.display = "inline-block";

  // Load dashboard data
  loadDashboardData();
}

function updateUIForLoggedOutUser() {
  loginBtn.style.display = "inline-block";
  registerBtn.style.display = "inline-block";
  logoutBtn.style.display = "none";
  dashboardLink.style.display = "none";

  // Hide dashboard
  document.getElementById("dashboard").style.display = "none";
}

// Payment functions
async function handlePayment() {
  if (!currentUser) {
    showToast("Please login to make a payment", "error");
    showAuthForm("login");
    return;
  }

  const amount = document.getElementById("amount").value;
  const description = document.getElementById("description").value;

  if (!amount || amount < 1) {
    showToast("Please enter a valid amount", "error");
    return;
  }

  try {
    showLoading(true);

    // Create order
    const orderResponse = await apiCall("/payment/create-order", {
      method: "POST",
      body: {
        amount: parseFloat(amount),
        items: [
          {
            name: description || "Payment",
            description: description || "Payment description",
            quantity: 1,
            price: parseFloat(amount),
          },
        ],
      },
    });

    const order = orderResponse.data.order;
    const razorpayOrder = orderResponse.data.razorpayOrder;

    // Get Razorpay configuration
    const configResponse = await apiCall("/payment/config");
    const razorpayKey = configResponse.data.key;

    // Initialize Razorpay payment
    const options = {
      key: razorpayKey,
      amount: razorpayOrder.amount,
      currency: razorpayOrder.currency,
      name: "PaymentApp",
      description: description || "Payment",
      order_id: razorpayOrder.id,
      handler: function (response) {
        verifyPayment(response);
      },
      prefill: {
        name: currentUser.name,
        email: currentUser.email,
        contact: currentUser.phone,
      },
      theme: {
        color: "#3399cc",
      },
      modal: {
        ondismiss: function () {
          showToast("Payment cancelled", "error");
        },
      },
    };

    const rzp = new Razorpay(options);
    rzp.open();
  } catch (error) {
    showToast(error.message, "error");
  } finally {
    showLoading(false);
  }
}

async function verifyPayment(response) {
  try {
    showLoading(true);

    const verifyResponse = await apiCall("/payment/verify", {
      method: "POST",
      body: {
        razorpay_order_id: response.razorpay_order_id,
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_signature: response.razorpay_signature,
      },
    });

    showToast("Payment successful!", "success");

    // Clear form
    document.getElementById("amount").value = "";
    document.getElementById("description").value = "";

    // Refresh dashboard
    loadDashboardData();

    // Show dashboard
    document.getElementById("dashboard").scrollIntoView({ behavior: "smooth" });
  } catch (error) {
    showToast("Payment verification failed: " + error.message, "error");
  } finally {
    showLoading(false);
  }
}

// Dashboard functions
async function loadDashboardData() {
  if (!currentUser) return;

  try {
    const ordersResponse = await apiCall("/orders");
    const orders = ordersResponse.data.orders;

    // Update stats
    const totalOrders = orders.length;
    const totalAmount = orders.reduce((sum, order) => sum + order.amount, 0);
    const successfulPayments = orders.filter(
      (order) => order.status === "paid"
    ).length;

    document.getElementById("totalOrders").textContent = totalOrders;
    document.getElementById("totalAmount").textContent =
      `₹${totalAmount.toFixed(2)}`;
    document.getElementById("successfulPayments").textContent =
      successfulPayments;

    // Update orders table
    updateOrdersTable(orders);

    // Show dashboard
    document.getElementById("dashboard").style.display = "block";
  } catch (error) {
    console.error("Failed to load dashboard data:", error);
    showToast("Failed to load dashboard data", "error");
  }
}

function updateOrdersTable(orders) {
  const tableBody = document.getElementById("ordersTableBody");

  if (orders.length === 0) {
    tableBody.innerHTML =
      '<tr><td colspan="5" style="text-align: center;">No orders found</td></tr>';
    return;
  }

  tableBody.innerHTML = orders
    .map(
      (order) => `
        <tr>
            <td>${order.orderId}</td>
            <td>₹${order.amount.toFixed(2)}</td>
            <td>
                <span class="status-badge status-${order.status}">
                    ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </span>
            </td>
            <td>${new Date(order.createdAt).toLocaleDateString()}</td>
            <td>
                <button class="btn btn-outline btn-small" onclick="viewOrder('${order._id}')">
                    View
                </button>
                ${
                  order.status === "created"
                    ? `<button class="btn btn-outline btn-small" onclick="cancelOrder('${order._id}')">Cancel</button>`
                    : ""
                }
            </td>
        </tr>
    `
    )
    .join("");
}

async function viewOrder(orderId) {
  try {
    const response = await apiCall(`/orders/${orderId}`);
    const order = response.data.order;
    const payment = response.data.payment;

    // Create order details modal (simplified)
    alert(
      `Order Details:\nID: ${order.orderId}\nAmount: ₹${order.amount}\nStatus: ${order.status}\nDate: ${new Date(order.createdAt).toLocaleString()}`
    );
  } catch (error) {
    showToast("Failed to load order details", "error");
  }
}

async function cancelOrder(orderId) {
  if (!confirm("Are you sure you want to cancel this order?")) return;

  try {
    await apiCall(`/orders/${orderId}/cancel`, { method: "PUT" });
    showToast("Order cancelled successfully", "success");
    loadDashboardData();
  } catch (error) {
    showToast("Failed to cancel order: " + error.message, "error");
  }
}

// Utility functions
function closeModal() {
  authModal.style.display = "none";
}

function showLoading(show) {
  loadingSpinner.style.display = show ? "flex" : "none";
}

function showToast(message, type = "info") {
  const toast = document.createElement("div");
  toast.className = `toast ${type}`;
  toast.textContent = message;

  toastContainer.appendChild(toast);

  setTimeout(() => {
    toast.remove();
  }, 5000);
}
