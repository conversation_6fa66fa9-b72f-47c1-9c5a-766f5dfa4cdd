const crypto = require("crypto");
const { v4: uuidv4 } = require("uuid");
const { razorpay, razorpayConfig } = require("../config/razorpay");
const Order = require("../models/Order");
const Payment = require("../models/Payment");

// @desc    Create Razorpay order
// @route   POST /api/payment/create-order
// @access  Private
const createOrder = async (req, res) => {
  try {
    const {
      amount,
      currency = "INR",
      items,
      shippingAddress,
      notes,
    } = req.body;

    // Validate amount
    if (!amount || amount < 1) {
      return res.status(400).json({
        success: false,
        message: "Amount must be greater than 0",
      });
    }

    // Check if Razorpay is configured
    if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
      return res.status(500).json({
        success: false,
        message:
          "Payment gateway not configured. Please check Razorpay credentials.",
      });
    }

    // Generate unique receipt
    const receipt = `${razorpayConfig.receipt_prefix}${Date.now()}`;
    const orderId = uuidv4();

    // Create Razorpay order
    const razorpayOrder = await razorpay.orders.create({
      amount: amount * 100, // Convert to paise
      currency,
      receipt,
      payment_capture: razorpayConfig.payment_capture,
      notes: notes || {},
    });

    // Save order to database
    const order = await Order.create({
      orderId,
      razorpayOrderId: razorpayOrder.id,
      user: req.user.id,
      amount,
      currency,
      receipt,
      notes,
      items: items || [],
      shippingAddress,
    });

    res.status(201).json({
      success: true,
      message: "Order created successfully",
      data: {
        order: {
          id: order._id,
          orderId: order.orderId,
          razorpayOrderId: razorpayOrder.id,
          amount: order.amount,
          currency: order.currency,
          status: order.status,
        },
        razorpayOrder,
      },
    });
  } catch (error) {
    console.error("Create order error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create order",
      error: error.message,
    });
  }
};

// @desc    Verify payment
// @route   POST /api/payment/verify
// @access  Private
const verifyPayment = async (req, res) => {
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
      req.body;

    // Verify signature
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac("sha256", process.env.RAZORPAY_KEY_SECRET)
      .update(body.toString())
      .digest("hex");

    const isAuthentic = expectedSignature === razorpay_signature;

    if (!isAuthentic) {
      return res.status(400).json({
        success: false,
        message: "Payment verification failed",
      });
    }

    // Find the order
    const order = await Order.findOne({ razorpayOrderId: razorpay_order_id });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Get payment details from Razorpay
    const paymentDetails = await razorpay.payments.fetch(razorpay_payment_id);

    // Update order status
    order.status = "paid";
    order.paymentId = razorpay_payment_id;
    order.signature = razorpay_signature;
    order.paidAt = new Date();
    await order.save();

    // Create payment record
    const payment = await Payment.create({
      razorpayPaymentId: razorpay_payment_id,
      razorpayOrderId: razorpay_order_id,
      razorpaySignature: razorpay_signature,
      order: order._id,
      user: req.user.id,
      amount: paymentDetails.amount / 100, // Convert from paise
      currency: paymentDetails.currency,
      status: paymentDetails.status,
      method: paymentDetails.method,
      bank: paymentDetails.bank,
      wallet: paymentDetails.wallet,
      vpa: paymentDetails.vpa,
      cardId: paymentDetails.card_id,
      fee: paymentDetails.fee / 100,
      tax: paymentDetails.tax / 100,
      captured: paymentDetails.captured,
      capturedAt: paymentDetails.captured ? new Date() : null,
    });

    res.json({
      success: true,
      message: "Payment verified successfully",
      data: {
        order,
        payment,
      },
    });
  } catch (error) {
    console.error("Payment verification error:", error);
    res.status(500).json({
      success: false,
      message: "Payment verification failed",
      error: error.message,
    });
  }
};

// @desc    Handle payment failure
// @route   POST /api/payment/failure
// @access  Private
const handlePaymentFailure = async (req, res) => {
  try {
    const { razorpay_order_id, error } = req.body;

    // Find and update order
    const order = await Order.findOne({ razorpayOrderId: razorpay_order_id });
    if (order) {
      order.status = "failed";
      order.failedAt = new Date();
      order.attempts += 1;
      await order.save();
    }

    res.json({
      success: true,
      message: "Payment failure recorded",
      data: {
        order,
      },
    });
  } catch (error) {
    console.error("Payment failure handling error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to handle payment failure",
    });
  }
};

// @desc    Get payment details
// @route   GET /api/payment/:paymentId
// @access  Private
const getPaymentDetails = async (req, res) => {
  try {
    const payment = await Payment.findOne({
      razorpayPaymentId: req.params.paymentId,
      user: req.user.id,
    }).populate("order");

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: "Payment not found",
      });
    }

    res.json({
      success: true,
      data: {
        payment,
      },
    });
  } catch (error) {
    console.error("Get payment details error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get payment details",
    });
  }
};

module.exports = {
  createOrder,
  verifyPayment,
  handlePaymentFailure,
  getPaymentDetails,
};
